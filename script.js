// Form handling
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('contactForm');
    const nameInput = document.getElementById('name');
    const phoneInput = document.getElementById('phone');

    // Phone number formatting
    phoneInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        
        if (value.length >= 9) {
            // Format as XXX XXX XXX
            value = value.replace(/(\d{3})(\d{3})(\d{3})/, '$1 $2 $3');
        } else if (value.length >= 6) {
            // Format as XXX XXX X...
            value = value.replace(/(\d{3})(\d{3})(\d+)/, '$1 $2 $3');
        } else if (value.length >= 3) {
            // Format as XXX X...
            value = value.replace(/(\d{3})(\d+)/, '$1 $2');
        }
        
        e.target.value = value;
    });

    // Form validation
    function validateForm() {
        const name = nameInput.value.trim();
        const phone = phoneInput.value.replace(/\D/g, '');

        if (name.length < 2) {
            showError('Imię musi mieć co najmniej 2 znaki');
            return false;
        }

        if (phone.length < 9) {
            showError('Podaj prawidłowy numer telefonu');
            return false;
        }

        return true;
    }

    // Error display
    function showError(message) {
        // Remove existing error messages
        const existingError = document.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }

        // Create and show error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.style.cssText = `
            background: #ef4444;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 14px;
            text-align: center;
        `;
        errorDiv.textContent = message;
        
        form.insertBefore(errorDiv, form.firstChild);
        
        // Remove error after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);
    }

    // Success message
    function showSuccess() {
        const formContainer = document.querySelector('.form-container');
        formContainer.innerHTML = `
            <div class="success-message" style="display: block;">
                <h2 style="color: white; margin-bottom: 15px;">✅ Dziękuję za kontakt!</h2>
                <p style="font-size: 16px; margin-bottom: 10px;">Odezwę się do Ciebie w ciągu 24 godzin.</p>
                <p style="font-size: 14px; opacity: 0.9;">Sprawdź też swoją skrzynkę e-mail (również spam).</p>
            </div>
        `;
    }

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        // Show loading state
        const submitButton = form.querySelector('.cta-button');
        const originalText = submitButton.innerHTML;
        submitButton.innerHTML = '<span>Wysyłam...</span>';
        submitButton.disabled = true;

        // Simulate form submission (replace with actual form handling)
        setTimeout(() => {
            // Here you would typically send the data to your server
            console.log('Form data:', {
                name: nameInput.value.trim(),
                phone: phoneInput.value
            });
            
            // Show success message
            showSuccess();
            
            // Track conversion (Google Analytics, Facebook Pixel, etc.)
            if (typeof gtag !== 'undefined') {
                gtag('event', 'conversion', {
                    'send_to': 'AW-CONVERSION_ID/CONVERSION_LABEL'
                });
            }
            
            if (typeof fbq !== 'undefined') {
                fbq('track', 'Lead');
            }
            
        }, 1500);
    });

    // Smooth scrolling for any anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add some interactive animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe benefit cards for animation
    document.querySelectorAll('.benefit-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});

// Add urgency timer (optional)
function updateUrgencyTimer() {
    const now = new Date();
    const endOfDay = new Date();
    endOfDay.setHours(23, 59, 59, 999);
    
    const timeLeft = endOfDay - now;
    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    
    const timerElement = document.querySelector('.urgency-timer');
    if (timerElement) {
        timerElement.textContent = `Oferta kończy się za: ${hours}h ${minutes}min`;
    }
}

// Update timer every minute
setInterval(updateUrgencyTimer, 60000);
updateUrgencyTimer();
