// Main application
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initStickyCtA();
    initFormHandling();
    initSmoothScrolling();
    initAnimations();
    initAnalytics();

    console.log('SocialBoost Landing Page initialized');
});

// Sticky CTA functionality
function initStickyCtA() {
    const stickyCtA = document.getElementById('sticky-cta');
    const heroSection = document.getElementById('hero');
    const ctaFormSection = document.getElementById('cta-form');

    if (!stickyCtA || !heroSection || !ctaFormSection) return;

    const observer = new IntersectionObserver((entries) => {
        const heroVisible = entries.find(entry => entry.target === heroSection)?.isIntersecting;
        const formVisible = entries.find(entry => entry.target === ctaFormSection)?.isIntersecting;

        // Show sticky CTA when hero is not visible and form is not visible
        if (!heroVisible && !formVisible) {
            stickyCtA.style.transform = 'translateY(0)';
        } else {
            stickyCtA.style.transform = 'translateY(100%)';
        }
    }, { threshold: 0.1 });

    observer.observe(heroSection);
    observer.observe(ctaFormSection);
}

// Scroll to form function
function scrollToForm() {
    const formSection = document.getElementById('cta-form');
    if (formSection) {
        formSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });

        // Track scroll to form event
        trackEvent('scroll_to_form', {
            'event_category': 'engagement',
            'event_label': 'cta_click'
        });
    }
}

// Form handling
function initFormHandling() {
    const form = document.getElementById('leadForm');
    if (!form) return;

    const inputs = {
        firstName: document.getElementById('firstName'),
        phone: document.getElementById('phone'),
        email: document.getElementById('email'),
        industry: document.getElementById('industry'),
        budget: document.getElementById('budget'),
        gdpr: document.getElementById('gdpr')
    };

    // Phone number formatting
    if (inputs.phone) {
        inputs.phone.addEventListener('input', formatPhoneNumber);
    }

    // Form validation
    form.addEventListener('submit', handleFormSubmit);

    // Track form interactions
    Object.values(inputs).forEach(input => {
        if (input && input.type !== 'checkbox') {
            input.addEventListener('focus', () => {
                trackEvent('form_start', {
                    'event_category': 'form',
                    'event_label': input.name
                });
            });
        }
    });
}

// Phone number formatting
function formatPhoneNumber(e) {
    let value = e.target.value.replace(/\D/g, '');

    // Add +48 prefix if not present
    if (value.length > 0 && !value.startsWith('48')) {
        if (value.length === 9) {
            value = '48' + value;
        }
    }

    // Format as +48 XXX XXX XXX
    if (value.length >= 11) {
        value = value.replace(/(\d{2})(\d{3})(\d{3})(\d{3})/, '+$1 $2 $3 $4');
    } else if (value.length >= 8) {
        value = value.replace(/(\d{2})(\d{3})(\d{3})(\d+)/, '+$1 $2 $3 $4');
    } else if (value.length >= 5) {
        value = value.replace(/(\d{2})(\d{3})(\d+)/, '+$1 $2 $3');
    } else if (value.length >= 2) {
        value = value.replace(/(\d{2})(\d+)/, '+$1 $2');
    }

    e.target.value = value;
}

// Form validation
function validateForm(formData) {
    const errors = [];

    if (!formData.firstName || formData.firstName.trim().length < 2) {
        errors.push('Imię i nazwisko musi mieć co najmniej 2 znaki');
    }

    const phoneDigits = formData.phone.replace(/\D/g, '');
    if (!phoneDigits || phoneDigits.length < 9) {
        errors.push('Podaj prawidłowy numer telefonu');
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email || !emailRegex.test(formData.email)) {
        errors.push('Podaj prawidłowy adres email');
    }

    if (!formData.gdpr) {
        errors.push('Musisz wyrazić zgodę na przetwarzanie danych osobowych');
    }

    return errors;
}

// Handle form submission
function handleFormSubmit(e) {
    e.preventDefault();

    const form = e.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    // Validate form
    const errors = validateForm(data);
    if (errors.length > 0) {
        showFormErrors(errors);
        return;
    }

    // Show loading state
    const submitButton = form.querySelector('.btn-form');
    const originalText = submitButton.innerHTML;
    submitButton.innerHTML = '<span>Wysyłam...</span>';
    submitButton.disabled = true;

    // Track form submission attempt
    trackEvent('form_submit_attempt', {
        'event_category': 'form',
        'event_label': 'lead_form'
    });

    // Submit form data
    submitFormData(data)
        .then(() => {
            showFormSuccess();
            trackConversion();
        })
        .catch((error) => {
            console.error('Form submission error:', error);
            showFormErrors(['Wystąpił błąd podczas wysyłania formularza. Spróbuj ponownie.']);
            submitButton.innerHTML = originalText;
            submitButton.disabled = false;
        });
}

// Submit form data to server
async function submitFormData(data) {
    // Replace with your actual endpoint
    const response = await fetch('/api/submit-lead', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    });

    if (!response.ok) {
        throw new Error('Network response was not ok');
    }

    return response.json();
}

// Show form errors
function showFormErrors(errors) {
    // Remove existing error messages
    const existingErrors = document.querySelectorAll('.form-error');
    existingErrors.forEach(error => error.remove());

    // Create error container
    const errorContainer = document.createElement('div');
    errorContainer.className = 'form-error';
    errorContainer.style.cssText = `
        background: #fee2e2;
        border: 1px solid #fecaca;
        color: #dc2626;
        padding: 16px;
        border-radius: 8px;
        margin-bottom: 20px;
        font-size: 14px;
    `;

    const errorList = document.createElement('ul');
    errorList.style.cssText = 'margin: 0; padding-left: 20px;';

    errors.forEach(error => {
        const li = document.createElement('li');
        li.textContent = error;
        errorList.appendChild(li);
    });

    errorContainer.appendChild(errorList);

    const form = document.getElementById('leadForm');
    form.insertBefore(errorContainer, form.firstChild);

    // Scroll to error
    errorContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// Show form success
function showFormSuccess() {
    const formContainer = document.querySelector('.cta-form .container');
    const successElement = document.getElementById('form-success');
    const leadForm = document.getElementById('leadForm');

    if (successElement && leadForm) {
        leadForm.style.display = 'none';
        successElement.style.display = 'block';

        // Scroll to success message
        successElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}

// Smooth scrolling
function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Animations
function initAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');

                // Track section view
                const sectionId = entry.target.id || entry.target.closest('section')?.id;
                if (sectionId) {
                    trackEvent('section_view', {
                        'event_category': 'engagement',
                        'event_label': sectionId
                    });
                }
            }
        });
    }, observerOptions);

    // Observe sections and cards for animation
    const elementsToAnimate = [
        '.case-card',
        '.service-card',
        '.why-us-card',
        '.testimonial',
        '.process-step',
        'section'
    ];

    elementsToAnimate.forEach(selector => {
        document.querySelectorAll(selector).forEach(element => {
            observer.observe(element);
        });
    });
}

// Analytics and tracking
function initAnalytics() {
    // Track page view
    trackEvent('page_view', {
        'page_title': document.title,
        'page_location': window.location.href
    });

    // Track scroll depth
    let maxScroll = 0;
    const trackScrollDepth = () => {
        const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);

        if (scrollPercent > maxScroll) {
            maxScroll = scrollPercent;

            // Track at 25%, 50%, 75%, 90% scroll depths
            if ([25, 50, 75, 90].includes(scrollPercent)) {
                trackEvent('scroll_depth', {
                    'event_category': 'engagement',
                    'event_label': `${scrollPercent}%`
                });
            }
        }
    };

    window.addEventListener('scroll', trackScrollDepth, { passive: true });
}

// Generic event tracking function
function trackEvent(eventName, parameters = {}) {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, parameters);
    }

    // Facebook Pixel
    if (typeof fbq !== 'undefined') {
        fbq('trackCustom', eventName, parameters);
    }

    console.log('Event tracked:', eventName, parameters);
}

// Track conversion
function trackConversion() {
    // Google Ads conversion
    if (typeof gtag !== 'undefined') {
        gtag('event', 'conversion', {
            'send_to': 'AW-CONVERSION_ID/CONVERSION_LABEL',
            'value': 500,
            'currency': 'PLN'
        });
    }

    // Facebook Pixel Lead event
    if (typeof fbq !== 'undefined') {
        fbq('track', 'Lead', {
            'value': 500,
            'currency': 'PLN',
            'content_name': 'Free Consultation'
        });
    }

    // GA4 Lead event
    trackEvent('generate_lead', {
        'event_category': 'conversion',
        'event_label': 'consultation_request',
        'value': 500
    });
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Make functions globally available
window.scrollToForm = scrollToForm;
