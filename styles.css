* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.header .container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px 20px;
}

.logo h1 {
    color: #2563eb;
    font-size: 24px;
    font-weight: 700;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.limited-offer {
    margin-bottom: 30px;
}

.offer-badge {
    background: #ff4757;
    color: white;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.spots-left {
    margin-top: 10px;
    font-size: 18px;
    color: #ffd700;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 20px;
}

.highlight {
    color: #ffd700;
}

.hero-subtitle {
    font-size: 20px;
    margin-bottom: 40px;
    opacity: 0.9;
    line-height: 1.5;
}

.services-preview {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.service-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 16px;
    font-weight: 500;
}

.service-icon {
    font-size: 20px;
}

/* Form */
.form-container {
    background: white;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    color: #333;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 10px;
    color: #2563eb;
}

.form-subtitle {
    color: #666;
    margin-bottom: 30px;
    font-size: 16px;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group input {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #2563eb;
}

.cta-button {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    color: white;
    border: none;
    padding: 18px 30px;
    border-radius: 12px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
}

.button-arrow {
    font-size: 20px;
    transition: transform 0.2s ease;
}

.cta-button:hover .button-arrow {
    transform: translateX(5px);
}

.privacy-note {
    font-size: 14px;
    color: #666;
    text-align: center;
    margin-top: 10px;
}

/* Benefits Section */
.benefits {
    padding: 80px 0;
    background: #f8fafc;
}

.benefits h2 {
    text-align: center;
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 50px;
    color: #1f2937;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
}

.benefit-card {
    background: white;
    padding: 40px 30px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.benefit-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.benefit-card h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #1f2937;
}

.benefit-card p {
    color: #666;
    font-size: 16px;
    line-height: 1.5;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 30px 0;
    text-align: center;
}

/* Success Message */
.success-message {
    background: #10b981;
    color: white;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    font-weight: 600;
    display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }
    
    .hero-title {
        font-size: 36px;
    }
    
    .hero-subtitle {
        font-size: 18px;
    }
    
    .services-preview {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .form-container {
        padding: 30px 20px;
    }
    
    .form-container h2 {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 100px 0 60px;
    }
    
    .hero-title {
        font-size: 28px;
    }
    
    .hero-subtitle {
        font-size: 16px;
    }
    
    .container {
        padding: 0 15px;
    }
}
