/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333333;
    background-color: #ffffff;
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
}

/* Typography - Mobile First */
h1 {
    font-size: 36px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 16px;
}

h2 {
    font-size: 24px;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 16px;
}

h3 {
    font-size: 20px;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 12px;
}

p {
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 16px;
}

/* Button System */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 14px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 44px;
    min-width: 44px;
    gap: 8px;
}

.btn-primary {
    background-color: #FF6B35;
    color: white;
}

.btn-primary:hover {
    background-color: #e55a2b;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.btn-large {
    padding: 18px 32px;
    font-size: 18px;
}

.btn-arrow::after {
    content: '→';
    margin-left: 8px;
    transition: transform 0.2s ease;
}

.btn:hover .btn-arrow::after {
    transform: translateX(4px);
}

/* Sticky CTA - Mobile First */
.sticky-cta {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 12px 16px;
    box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: block;
}

.sticky-cta .btn {
    width: 100%;
    justify-content: center;
}

/* Hero Section - Mobile First */
.hero {
    background: linear-gradient(135deg, #667EEA 0%, #764BA2 100%);
    color: white;
    padding: 60px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-content {
    display: flex;
    flex-direction: column;
    gap: 40px;
    text-align: center;
}

.hero-text {
    order: 1;
}

.hero-visual {
    order: 2;
}

.hero-title {
    font-size: 36px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 16px;
}

.hero-subtitle {
    font-size: 18px;
    margin-bottom: 24px;
    opacity: 0.95;
    line-height: 1.5;
}

.value-props {
    list-style: none;
    margin-bottom: 32px;
    text-align: left;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.value-props li {
    font-size: 16px;
    margin-bottom: 12px;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.hero-cta {
    margin-bottom: 32px;
}

.cta-microcopy {
    font-size: 14px;
    margin-top: 8px;
    opacity: 0.8;
    margin-bottom: 0;
}

.trust-indicators {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 24px;
    margin-bottom: 32px;
}

.trust-item {
    text-align: center;
    min-width: 80px;
}

.trust-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #2ECC71;
}

.trust-label {
    font-size: 12px;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hero-image {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Social Proof Section */
.social-proof {
    padding: 60px 0;
    background: #f8fafc;
}

.section-title {
    text-align: center;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 32px;
    color: #1f2937;
}

.section-subtitle {
    text-align: center;
    font-size: 16px;
    color: #6b7280;
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.trust-badges {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
    margin-bottom: 40px;
}

.badge {
    background: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.client-logos {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 24px;
    margin-bottom: 48px;
    opacity: 0.6;
}

.client-logos img {
    height: 32px;
    width: auto;
    filter: grayscale(100%);
    transition: filter 0.3s ease;
}

.client-logos img:hover {
    filter: grayscale(0%);
}

.testimonials {
    display: grid;
    gap: 24px;
}

.testimonial {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.testimonial-content p {
    font-size: 16px;
    line-height: 1.6;
    color: #374151;
    margin-bottom: 16px;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 12px;
}

.testimonial-author img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
}

.testimonial-author strong {
    display: block;
    font-weight: 600;
    color: #1f2937;
}

.testimonial-author span {
    font-size: 14px;
    color: #6b7280;
}

/* Case Studies Section */
.case-studies {
    padding: 60px 0;
    background: white;
}

.cases-grid {
    display: grid;
    gap: 24px;
}

.case-card {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 24px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.case-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.case-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.case-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0;
}

.case-budget {
    font-size: 14px;
    color: #6b7280;
    background: white;
    padding: 4px 12px;
    border-radius: 16px;
    border: 1px solid #d1d5db;
}

.case-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 16px;
    margin-bottom: 16px;
}

.metric {
    text-align: center;
}

.metric-value {
    display: block;
    font-size: 20px;
    font-weight: 700;
    color: #2ECC71;
    margin-bottom: 4px;
}

.metric-label {
    font-size: 12px;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.case-strategy {
    font-size: 14px;
    color: #374151;
    padding-top: 16px;
    border-top: 1px solid #e5e7eb;
}

/* Services Section */
.services {
    padding: 60px 0;
    background: #f8fafc;
}

.services-grid {
    display: grid;
    gap: 24px;
}

.service-card {
    background: white;
    padding: 32px 24px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    text-align: center;
}

.service-icon {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
}

.service-card h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 12px;
}

.service-promise {
    font-size: 16px;
    font-weight: 600;
    color: #2ECC71;
    margin-bottom: 16px;
}

.service-features {
    list-style: none;
    text-align: left;
}

.service-features li {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 8px;
    padding-left: 16px;
    position: relative;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #2ECC71;
    font-weight: 600;
}

/* Why Us Section */
.why-us {
    padding: 60px 0;
    background: white;
}

.why-us-grid {
    display: grid;
    gap: 24px;
}

.why-us-card {
    background: #f8fafc;
    padding: 24px;
    border-radius: 12px;
    text-align: center;
    border: 1px solid #e5e7eb;
}

.why-us-icon {
    font-size: 40px;
    margin-bottom: 16px;
    display: block;
}

.why-us-card h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 12px;
}

.why-us-card p {
    font-size: 14px;
    color: #6b7280;
    line-height: 1.5;
}

/* Process Section */
.process {
    padding: 60px 0;
    background: #f8fafc;
}

.process-steps {
    display: grid;
    gap: 24px;
}

.process-step {
    display: flex;
    gap: 16px;
    align-items: flex-start;
}

.step-number {
    width: 40px;
    height: 40px;
    background: #FF6B35;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 18px;
    flex-shrink: 0;
}

.step-content h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
}

.step-content p {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 8px;
}

.step-timeline {
    font-size: 12px;
    color: #9ca3af;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* CTA Form Section */
.cta-form {
    padding: 60px 0 100px;
    background: linear-gradient(135deg, #667EEA 0%, #764BA2 100%);
    color: white;
}

.cta-content {
    text-align: center;
    margin-bottom: 40px;
}

.cta-title {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 16px;
}

.cta-subtitle {
    font-size: 18px;
    margin-bottom: 24px;
    opacity: 0.95;
}

.value-highlight {
    color: #2ECC71;
    font-weight: 600;
}

.urgency-indicators {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
    margin-bottom: 32px;
}

.urgency-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.lead-form {
    background: white;
    padding: 32px 24px;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    color: #333333;
    max-width: 600px;
    margin: 0 auto;
}

.form-row {
    display: grid;
    gap: 16px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.form-group input,
.form-group select {
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.2s ease;
    min-height: 44px;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #FF6B35;
}

.form-checkbox {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 24px;
}

.form-checkbox input[type="checkbox"] {
    margin-top: 2px;
    min-width: 16px;
    min-height: 16px;
}

.form-checkbox label {
    font-size: 14px;
    color: #6b7280;
    line-height: 1.4;
}

.form-checkbox a {
    color: #FF6B35;
    text-decoration: underline;
}

.btn-form {
    width: 100%;
    margin-bottom: 16px;
}

.form-security {
    text-align: center;
    font-size: 14px;
    color: #6b7280;
}

.form-success {
    background: white;
    padding: 32px 24px;
    border-radius: 12px;
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
    color: #333333;
}

.success-icon {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
}

.form-success h3 {
    color: #2ECC71;
    margin-bottom: 16px;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    gap: 24px;
    margin-bottom: 24px;
    text-align: center;
}

.footer-brand h3 {
    color: #FF6B35;
    margin-bottom: 8px;
}

.footer-brand p {
    color: #9ca3af;
    font-size: 14px;
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
}

.footer-links a {
    color: #d1d5db;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: #FF6B35;
}

.footer-contact p {
    font-size: 14px;
    color: #9ca3af;
    margin-bottom: 4px;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #374151;
}

.footer-bottom p {
    font-size: 14px;
    color: #9ca3af;
    margin-bottom: 0;
}

/* Responsive Design - Tablet */
@media (min-width: 768px) {
    .container {
        padding: 0 32px;
    }

    h1 {
        font-size: 48px;
    }

    h2 {
        font-size: 32px;
    }

    .sticky-cta {
        display: none;
    }

    .hero {
        padding: 80px 0 100px;
    }

    .hero-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 60px;
        align-items: center;
        text-align: left;
    }

    .hero-text {
        order: 1;
    }

    .hero-visual {
        order: 2;
    }

    .hero-title {
        font-size: 48px;
    }

    .hero-subtitle {
        font-size: 20px;
    }

    .value-props {
        text-align: left;
        margin-left: 0;
        margin-right: 0;
        max-width: none;
    }

    .trust-indicators {
        justify-content: flex-start;
    }

    .testimonials {
        grid-template-columns: repeat(2, 1fr);
        gap: 32px;
    }

    .cases-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 32px;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 32px;
    }

    .why-us-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 32px;
    }

    .process-steps {
        grid-template-columns: repeat(2, 1fr);
        gap: 40px;
    }

    .form-row {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .form-row:first-child {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: repeat(3, 1fr);
        text-align: left;
    }
}

/* Responsive Design - Desktop */
@media (min-width: 1024px) {
    .section-title {
        font-size: 36px;
    }

    .hero-content {
        gap: 80px;
    }

    .testimonials {
        grid-template-columns: repeat(3, 1fr);
    }

    .cases-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .services-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .why-us-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 40px;
    }

    .process-steps {
        grid-template-columns: repeat(4, 1fr);
        gap: 32px;
    }

    .process-step {
        flex-direction: column;
        text-align: center;
    }

    .step-number {
        margin: 0 auto 16px;
    }
}

/* Large Desktop */
@media (min-width: 1200px) {
    .cases-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .why-us-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Animations */
.case-card,
.service-card,
.why-us-card,
.testimonial,
.process-step {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.case-card.animate-in,
.service-card.animate-in,
.why-us-card.animate-in,
.testimonial.animate-in,
.process-step.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Sticky CTA Animation */
.sticky-cta {
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

/* Form Error Styles */
.form-error {
    animation: slideInDown 0.3s ease;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading States */
.btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Focus States for Accessibility */
.btn:focus,
input:focus,
select:focus {
    outline: 2px solid #FF6B35;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .btn-primary {
        border: 2px solid currentColor;
    }

    .case-card,
    .service-card,
    .why-us-card,
    .testimonial {
        border: 1px solid #333333;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .case-card,
    .service-card,
    .why-us-card,
    .testimonial,
    .process-step {
        opacity: 1;
        transform: none;
    }
}
